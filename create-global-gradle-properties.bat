@echo off
echo Creating global Gradle properties to disable toolchain download...

if not exist "%USERPROFILE%\.gradle" mkdir "%USERPROFILE%\.gradle"

echo # Global Gradle settings to disable toolchain download > "%USERPROFILE%\.gradle\gradle.properties"
echo org.gradle.java.installations.auto-detection=false >> "%USERPROFILE%\.gradle\gradle.properties"
echo org.gradle.java.installations.auto-download=false >> "%USERPROFILE%\.gradle\gradle.properties"
echo org.gradle.java.installations.fromEnv=JAVA_HOME >> "%USERPROFILE%\.gradle\gradle.properties"

echo Global Gradle properties created at: %USERPROFILE%\.gradle\gradle.properties
echo.
echo Contents:
type "%USERPROFILE%\.gradle\gradle.properties"

pause
