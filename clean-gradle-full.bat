@echo off
echo WARNING: This will clean ALL Gradle cache and affect ALL projects!
echo This includes your Gradle 6.5 version used by other projects.
echo.
set /p confirm="Are you sure you want to continue? (y/N): "
if /i not "%confirm%"=="y" (
    echo Cancelled.
    pause
    exit /b
)

echo Cleaning ALL Gradle cache and build files...

echo Stopping Gradle daemon...
cd android
call gradlew --stop

echo Cleaning project...
call gradlew clean

echo Cleaning ALL Gradle cache (affects all projects)...
rmdir /s /q "%USERPROFILE%\.gradle\caches" 2>nul
rmdir /s /q "%USERPROFILE%\.gradle\wrapper\dists" 2>nul

echo Cleaning local build files...
rmdir /s /q "build" 2>nul
rmdir /s /q "app\build" 2>nul
rmdir /s /q ".gradle" 2>nul

cd ..

echo Cleaning node_modules and npm cache...
rmdir /s /q "node_modules" 2>nul
npm cache clean --force
npm install

echo Done! All Gradle versions will be re-downloaded when needed.
pause
