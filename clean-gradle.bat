@echo off
echo Cleaning local build files only (preserving global Gradle cache)...

echo Stopping Gradle daemon...
cd android
call gradlew --stop

echo Cleaning project...
call gradlew clean

echo Cleaning local build files...
rmdir /s /q "build" 2>nul
rmdir /s /q "app\build" 2>nul
rmdir /s /q ".gradle" 2>nul

cd ..

echo Cleaning node_modules and npm cache...
rmdir /s /q "node_modules" 2>nul
npm cache clean --force
npm install

echo Done! You can now try running: npm run android
echo.
echo Note: Global Gradle cache preserved to avoid affecting other projects
pause
