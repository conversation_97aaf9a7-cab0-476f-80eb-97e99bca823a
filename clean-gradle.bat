@echo off
echo Cleaning Gradle cache and build files...

echo Stopping Gradle daemon...
cd android
call gradlew --stop

echo Cleaning project...
call gradlew clean

echo Cleaning Gradle cache...
rmdir /s /q "%USERPROFILE%\.gradle\caches" 2>nul
rmdir /s /q "%USERPROFILE%\.gradle\wrapper\dists" 2>nul

echo Cleaning local build files...
rmdir /s /q "build" 2>nul
rmdir /s /q "app\build" 2>nul

cd ..

echo Cleaning npm cache...
npm cache clean --force

echo Done! You can now try running: npm run android
pause
