allprojects {
    repositories {
        // 阿里云镜像源
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/central' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url 'https://maven.aliyun.com/repository/public' }
        
        // 腾讯云镜像源（备用）
        maven { url 'https://mirrors.cloud.tencent.com/nexus/repository/maven-public/' }
        
        // 原始仓库作为备用
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

gradle.projectsLoaded {
    rootProject.allprojects {
        repositories {
            // 阿里云镜像源
            maven { url 'https://maven.aliyun.com/repository/google' }
            maven { url 'https://maven.aliyun.com/repository/central' }
            maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
            maven { url 'https://maven.aliyun.com/repository/public' }
            
            // 腾讯云镜像源（备用）
            maven { url 'https://mirrors.cloud.tencent.com/nexus/repository/maven-public/' }
            
            // 原始仓库作为备用
            google()
            mavenCentral()
            gradlePluginPortal()
        }
    }
}
