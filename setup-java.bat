@echo off
echo Checking Java installation...

echo Current Java version:
java -version
echo.

echo Current JAVA_HOME: %JAVA_HOME%
echo.

echo Looking for Java installations...
if exist "C:\Program Files\Java" (
    echo Found Java installations in C:\Program Files\Java:
    dir "C:\Program Files\Java" /b
    echo.
)

if exist "C:\Program Files (x86)\Java" (
    echo Found Java installations in C:\Program Files (x86)\Java:
    dir "C:\Program Files (x86)\Java" /b
    echo.
)

echo.
echo For React Native 0.81 with Gradle 7.6.4, you need:
echo - JDK 8 (minimum) or JDK 11 (recommended)
echo.
echo Current setup should work with Gradle 7.6.4
echo.
echo If you want to set JAVA_HOME permanently, run as administrator:
echo setx JAVA_HOME "C:\Program Files\Java\jdk1.8.0_411" /M
echo.
echo Or for current session only:
echo set JAVA_HOME=C:\Program Files\Java\jdk1.8.0_411
echo.
pause
