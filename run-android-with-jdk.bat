@echo off
echo Setting up Java environment for React Native build...

echo Setting JAVA_HOME to JDK 1.8...
set JAVA_HOME=C:\Program Files\Java\jdk-1.8
set PATH=%JAVA_HOME%\bin;%PATH%

echo Disabling Gradle toolchain download for this session...
set GRADLE_OPTS=-Dorg.gradle.java.installations.auto-detection=false -Dorg.gradle.java.installations.auto-download=false

echo Verifying Java setup...
echo JAVA_HOME: %JAVA_HOME%
java -version
javac -version

echo.
echo Starting React Native Android build...
npm run android

pause
