@echo off
echo Testing Android build configuration...

echo Setting JAVA_HOME...
set JAVA_HOME=C:\Program Files\Java\jdk-1.8
set PATH=%JAVA_HOME%\bin;%PATH%

echo Current Java version:
java -version
echo.

echo Current Gradle version:
cd android
call gradlew --version
echo.

echo Testing Gradle sync...
call gradlew tasks --all
echo.

echo If no errors above, try building:
echo gradlew assembleDebug

pause
