@echo off
echo Setting up environment for Android Studio...

echo Setting JAVA_HOME to JDK 1.8...
set JAVA_HOME=C:\Program Files\Java\jdk-1.8
set PATH=%JAVA_HOME%\bin;%PATH%

echo Disabling Gradle toolchain download...
set GRADLE_OPTS=-Dorg.gradle.java.installations.auto-detection=false -Dorg.gradle.java.installations.auto-download=false

echo Environment variables set:
echo JAVA_HOME: %JAVA_HOME%
echo GRADLE_OPTS: %GRADLE_OPTS%
echo.

echo Starting Android Studio with proper environment...
echo Please open the android folder in Android Studio
echo.

echo If you want to test <PERSON>radle directly, run:
echo cd android
echo gradlew tasks
echo.

pause
